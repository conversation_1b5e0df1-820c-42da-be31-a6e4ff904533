<?php

  const PROJECT = 'indufast';

  const DIR_PROJECT_FOLDER = DIR_ROOT . 'projects/' . PROJECT . '/';
  const URL_PROJECT_FOLDER = '/projects/' . PROJECT . '/';
  const URL_UPLOADS = '/uploads/' . PROJECT . '/';
  const DIR_UPLOADS = DIR_ROOT . 'uploads/' . PROJECT . '/';
  const URL_TEMP = '/temp/';
  const DIR_TEMP = DIR_ROOT . 'temp/';
  const DIR_ROOT_GSDFW = DIR_ROOT . 'gsdfw/';
  const DIR_CLASSES = DIR_ROOT_GSDFW . 'includes/classes/';
  const URL_INCLUDES = '/gsdfw/includes/';
  const DIR_INCLUDES = DIR_ROOT_GSDFW . 'includes/';
  const DIR_MODULES = DIR_ROOT_GSDFW . 'modules/';

  const DIR_PLUGIN_FOLDER = DIR_ROOT_GSDFW . 'plugins/';
  const URL_PLUGIN_FOLDER = '/gsdfw/plugins/';
  const CREDITS_ENABLED = false; //gebruik credit systeem
  Config::set('ORDER_USE_REVISIONNR', true);

  const CONFIGURE_ADDPRODUCTS_IN_ROOTDIR = true; //Voeg producten toe aan de root.
  const CONFIGURE_MAX_DEPTH_NESTED_CATS = 4; //Maximaal genestte niveau van categorieën.
  const CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS = false; //Hetzelfde product onder meerdere categorieën hangen.
  Config::set('CONFIGURE_ADD_PRODUCT_WITHOUT_PRICE', false); //Product hoeft geen prijs te hebben.

  const DIR_LANGUAGES = DIR_ROOT_GSDFW . 'includes/languages/';
  const DIR_LANGUAGES_PROJECT = DIR_PROJECT_FOLDER . 'languages/';
  const LOG_FOLDER = DIR_ROOT . "logs/";
  const LOGLEVEL = "DEBUG";//DEBUG is only active string at this moment

  const HASH_STRING = '#34tkdf';
  const ADMIN_DEFAULT_ID = 2;

  const DIR_UPLOAD_CAT = DIR_UPLOADS . 'images/catalog/';
  const URL_UPLOAD_CAT = URL_UPLOADS . 'images/catalog/';
  const DIR_UPLOAD_BRAND = DIR_UPLOADS . 'images/brands/';
  const URL_UPLOAD_BRAND = URL_UPLOADS . 'images/brands/';
  const PAGES_IMAGES_RESIZE = false;
  const IMAGES_THUMB_WIDTH = 360; //product image
  const IMAGES_THUMB_HEIGHT = 207; //product image
  const IMAGES_ORIG_WIDTH = 1920; //product image
  const IMAGES_ORIG_HEIGHT = 1280; //product image
  const IMAGES_BRAND_THUMB_WIDTH = 170; //brand image
  const IMAGES_BRAND_THUMB_HEIGHT = 100; //brand image
  const IMAGES_BRAND_ORIG_WIDTH = 700; //brand image
  const IMAGES_BRAND_ORIG_HEIGHT = 600; //brand image
  const IMAGES_CAT_ORIG_RESIZE = false; //cat image
  const IMAGES_CAT_THUMB_WIDTH = 240; //cat image
  const IMAGES_CAT_THUMB_HEIGHT = 190; //cat image
  const IMAGES_CAT_ORIG_WIDTH = 690; //cat image
  const IMAGES_CAT_ORIG_HEIGHT = 280; //cat image
  const IMAGES_PAGE_THUMB_WIDTH = 360; //page image
  const IMAGES_PAGE_THUMB_HEIGHT = 600; //page image
  const IMAGES_PAGE_PREVIEW_WIDTH = 1920; //page image
  const IMAGES_PAGE_PREVIEW_HEIGHT = 1280; //page image
  const IMAGES_PAGE_THUMBPREVIEW_WIDTH = 130; //page image
  const IMAGES_PAGE_THUMBPREVIEW_HEIGHT = 130; //page image
  const IMAGES_CAR_THUMB_WIDTH = 250; //car image
  const IMAGES_CAR_THUMB_HEIGHT = 188; //car image
  const IMAGES_CAR_PREVIEW_WIDTH = 700; //car image
  const IMAGES_CAR_PREVIEW_HEIGHT = 600; //car image
  const IMAGES_LOGO_THUMB_WIDTH = 203; //bedrijfslogo
  const IMAGES_LOGO_THUMB_HEIGHT = 100; //bedrijfslogo
  const IMAGES_LOGO_PREV_WIDTH = 203; //bedrijfslogo
  const IMAGES_LOGO_PREV_HEIGHT = 100; //bedrijfslogo
  const IMAGES_LOGO_ORIG_WIDTH = 500; //bedrijfslogo
  const IMAGES_LOGO_ORIG_HEIGHT = 500; //bedrijfslogo

  const DIR_UPLOAD_PDF = DIR_UPLOADS . 'pdf/';
  const URL_UPLOAD_PDF = URL_UPLOADS . 'pdf/';

  const DIR_SITE = DIR_PROJECT_FOLDER . 'sites/';
  const URL_SITE = URL_PROJECT_FOLDER . 'sites/';
  const DIR_TEMPLATE = DIR_PROJECT_FOLDER . 'templates/';
  const URL_TEMPLATE = URL_PROJECT_FOLDER . 'templates/';
  const DIR_UPLOADS_SITE = DIR_UPLOADS . 'sites/';
  const URL_UPLOADS_SITE = URL_UPLOADS . 'sites/';

  Config::set("USER_LIST_SHOW_DELETE", true);
  Config::set("LOG_ALL_MAIL_TO_DB", true);
  Config::set("USER_VOID_ON_DESTROY", true);

  //pagina beheer

  const DISABLE_1STLEVEL_CREATENEW = false;
  const DISABLE_1STLEVEL_EDIT = false;
  const DISABLE_1STLEVEL_DELETE = false;
  const DISABLE_1STLEVEL_MOVE = false;

  Config::set('PAGE_GALLERY_ENABLED', true); //Er mogen foto gallerijen aangemaakt worden. (tab: Gallerij in pagina beheer)

  const CALCULATE_SHIPPING_CAT = false; //Verzendkosten codes, bijv: A = €5, B = €7,50, etc.
  const STOCK_ENABLED = false;
  Config::set('PRODUCT_DISCOUNT_PRICE', false);
  Config::set('USE_PRICE_KAPSALONS_AS_INSTALLATION_COSTS', true); //gebruik price_kapsalon voor montagekosten per product
  Config::set('PRODUCT_PRICE_BUY_DISABLED', true);
  Config::set("PRODUCT_USE_BRUTO_PRICE", false); //Men kan adviesprijs (bruto prijs) invoeren bij product.
  Config::set("PRODUCT_FILES_MULTIPLE", true);
  Config::set("USE_PRODUCTFILES_PDF_FILE_TITLE", true);

  const SEOTITLE = true;
  const SEODESCRIPTION = true;
  const TAGS = true;
  const TAGS_PRODUCT = false;

  const NROFNAVIGATIONLEVELS = 5;
  const IMAGES_EXIF = false; //retrieve lat/long of image

  const GSD_ORGANISATION_ID = 1;
  const INDUFAST_ORGANISATION_ID = 2;

  Config::set("catalog_languages", ['nl']); //languages for catalog/product descriptions
  Config::set("organisation_types_usg", [
    'ADMIN' => ['BEDRIJF','PARTICULIER', 'VIEWER'],
  ]); //welke organisatie.types mag een usergroup aanmaken [USER.USERGROUP=>[ORGANISATION.TYPE,ORGANISATION.TYPE,...]]
  Config::set("usergroups_for_usg", [
    'ADMIN' => ['BEDRIJF','PARTICULIER', 'VIEWER'],
  ]); //welke gebruikersgroepen mag een bepaalde gebruikersgroep zien/bewerken [USER.USERGROUP=>[USER.USERGROUP,USER.USERGROUP,...]]

  Config::set("countries", ['nl' => 'Nederland', 'be' => 'België', 'de' => "Duitsland", 'fr' => "Frankrijk"]); //countries possible

  const CATALOG_BUY_DISCOUNT = false; //inkoopkorting aangeven
  const CATALOG_BRAND = false; //producten aan merken koppelen
  Config::set('CATALOG_PRODUCT_BTWGROUP', [
    'nl' => [1 => 6, 2 => 21],
    'be' => [1 => 21, 2 => 21],
  ]); //producten aan btwgroep kunnen koppelen

  Config::set('PAGES_MAX_PAGE_COUNT', [
    'free'     => 0,
    'standard' => 100,
    'large'    => 100,
  ]); //maximale aantal pagina's aan te maken per abo
  Config::set('ORGANISATION_EMAIL_OBLIDGED', false); //organisatie is verplicht emailadres in te vullen


  Config::set("PAGES_FRONTEND_IMAGES_PER_ROW", 3); //aantal beelden per rij
  Config::set('FRONTEND_PAGER_SIZE', 24); //aantal producten per pagina

  Config::set('PAGE_CKEDITOR_TOOLBAR', 'Full'); //Simple/Full toolbar to load ckeditor
  Config::set('PAGE_CKEDITOR_FILEBROWSER', true); //use ckeditor filebrowser
  Config::set('PAGE_IMAGES_NOT_RESIZE_PAGEIDS', [8]); //paginaids van pagina's waarvan we de large formaat niet aanpassen. Bijv fotoslider pagina
  Config::set('PAGE_IMAGES_CKEDITOR_PAGEIDS', true); //paginaids van pagina's waarvan we de textarea vervangen door de ckeditor

  //CROPPING TOOL
  Config::set('PAGE_IMAGES_CROPPING_TOOL', [4]); //'ALL', paginaids van pagina's waarvan we de beelden kunnen croppen middels de croppingtool.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_TO_RESIZE', ['ORIG' => 'ORIG']); //Welke formaten er met de tool gecropt mogen worden.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_ORIG', [4]); //'ALL' || pageids OR parent_id. Bij de opgegeven paginaids zal de large(orig) foto worden overschreven na cropping.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_THUMB', 'ALL'); //'ALL' || pageids OR parent_id. Bij de opgegeven paginaids zal de thumb foto worden overschreven na cropping.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_ORIG_SIZE', [
    4 => [
      430,
      300,
    ],
  ]); //'ALL' || pageid || parent_id. Array(Breedte, Hoogte). Naar welke aspect-ratio en grootte de large foto gerescaled of gecropt dient te worden.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_THUMB_SIZE', [
    IMAGES_PAGE_THUMB_WIDTH,
    IMAGES_PAGE_THUMB_HEIGHT,
  ]); //Breedte, Hoogte. Naar welke aspect-ratio en grootte de thumb foto gerescaled of gecropt dient te worden.


  Config::set("PAGE_TEASER_ENABLED", true); //page id || parent_id waarvoor teaser tekst + plaatje ingevoerd kunnen worden

  Config::set("ORGAN_EDIT_SHOW_MAP", true);
  Config::set("ORGANISATION_INVOICE_TYPE_EDITABLE", false);

  Config::set('PAGE_CUSTOM_URL', true); //19-09-2015 - Robert - mogelijkheid om een zelf de url in voeren.
  Config::set("ORGAN_EDIT_SHOW_SOCIAL_MEDIA", false); //true/false. Toont Social media inputvelden bij bewerken organisatie.
  Config::set("ADMIN_UPLOAD_ORGAN_LOGO", false); //15-08-2018 - ROB - company logo can be added/removed from relations

  Config::set("GSDFW_BACKEND_PRIVILIGES_OPEN", ['M_API' => 'api']); //dit zijn de pagina id's welke extern bereikbaar zijn zonder standaard authenticatie id=>name
  Config::set("USER_PASSWORD_ENCRYPT", true); //01-04-2016 - KOEN - stores encrypted passwords in DB. Let op gebruikt HASH_STRING config.
  Config::set("CRYPT_SALT_KEY", "51fZCBCmWY5vJnJ+S9yHZMk5DEBn+tqKKZRQgJHfTgs=");
  Config::set("CRYPT_SALT_IV", "55RWMpBpuCIiR7QpK5VOXg==");

  Config::set('USER_PROFILE_CODES', [
    'accredis-driver-name' => "Naam in Accredis",
  ]);

  Config::set("PRIVILEGES", ["ACTIVE" => true]);
  Config::set('GOOGLE_API_MAPS_KEY', 'AIzaSyBp3y4GsI4QSKHbYqQGaNBHuMYxzR3yJbA');

  Config::set('GOOGLE_API_AUTH_CONFIG', __DIR__ . '/indufast-sait-df80395e9467.json');
  Config::set('GOOGLE_API_OAUTH_CONFIG', __DIR__ . '/indufast-sait-oauth-secret.json');
  Config::set('GOOGLE_API_PICKER_CONFIG', __DIR__ . '/indufast-sait-picker-config.json');
  Config::set('GOOGLE_API_SUBJECT', '<EMAIL>');
  Config::set('GOOGLE_API_CALENDAR_ID', '<EMAIL>');
  require_once __DIR__ . '/../vendor/autoload.php';