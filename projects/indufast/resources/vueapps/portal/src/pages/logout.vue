<script setup>
import {onMounted} from "vue";
import {useAuthenticationStore} from "@/stores/authentication.js";
import {useRouter} from "vue-router";
import createApiService from "@/services/api.js";

const authStore = useAuthenticationStore();
const router = useRouter();
const api = createApiService(router);

onMounted(() => {
  api.post("/logout").then(() => {
    authStore.logOut();
    router.push({name: "/"});
  })
});
</script>
