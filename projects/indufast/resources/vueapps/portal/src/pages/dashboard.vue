<script setup>
import {definePage} from "unplugin-vue-router/runtime";
import {useAuthenticationStore} from '@/stores/authentication.js';
import {onMounted, ref} from "vue";
import {format} from "date-fns";
import createApiService from "@/services/api.js";
import {useRouter} from "vue-router";

const router = useRouter();
const api = createApiService(router);
const projects = ref([]);

definePage({
  meta: {
    title: 'Dashboard',
    requiresAuth: true,
  },
})

onMounted(() => {
  updateProjects();
});

const updateProjects = () => {
  api
    .get("projectList?" + new URLSearchParams({
      archive: false,
      date: format(new Date(), 'yyyy-MM-dd'),
    }).toString())
    .then((response) => {
      projects.value = response.data.data;
    })
    .catch((error) => {
      console.log(error);
    });
}

const authStore = useAuthenticationStore();
</script>

<template>
  <v-card>
    <v-card-title>
      Welkom bij de Indufast Portal, {{ authStore.getCurrentUserName() }}
    </v-card-title>
    <v-card-text>
      <p>Het is vandaag <b>{{ $filters.formatDate(new Date()) }}</b>.</p>
      <v-container fluid class="pa-0 mt-4">
        <v-row dense>
          <v-col cols="2">
            Aantal projecten
          </v-col>
          <v-col>
            {{ projects.length }}
          </v-col>
        </v-row>
        <v-row dense>
          <v-col cols="2">
            Laatste import Accredis-data
          </v-col>
          <v-col>
            @TODO
          </v-col>
        </v-row>
        <v-row dense>
          <v-col cols="2">
            Aantal te controleren werkdagen
          </v-col>
          <v-col>
            @TODO
          </v-col>
        </v-row>
      </v-container>
    </v-card-text>
  </v-card>
</template>
