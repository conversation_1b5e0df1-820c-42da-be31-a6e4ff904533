<script setup>
import {computed, onMounted, ref} from "vue";
import {useRouter} from 'vue-router';
import createApiService from '@/services/api';
import {definePage} from "unplugin-vue-router/runtime";
import {materialLoadTypes, plannable, projectStatus} from "@/helpers/constants.js";
import {sortNullLast} from "@/helpers/helpers.js";
import ProjectEditDialog from "@/components/project/ProjectEditDialog.vue";
import SearchBox from "@/components/SearchBox.vue";
import {useAuthenticationStore} from "@/stores/authentication.js";

const router = useRouter();
const api = createApiService(router);
const authStore = useAuthenticationStore();
const projects = ref([]);
const loading = ref(false);
const archive = ref(false);
const showEditProjectDialog = ref(false);
const currentProject = ref({});
const errors = ref({});
const search = ref('');

definePage({
  meta: {
    title: 'Projecten',
  },
})

onMounted(() => {
  updateProjects()
});

const sortStatus = (a, b) => {
  return projectStatus.find(status => status.value === a)?.title.localeCompare(projectStatus.find(status => status.value === b)?.title);
}

const projectHeader = [
  {title: "Naam", value: "name", maxWidth: '350', nowrap: true, sortable: true},
  {title: "FTE", value: "fte", cellProps: {class: "no-wrap"}, sortable: true, sort: sortNullLast},
  {title: "Projectnummer", value: "project_number", cellProps: {class: "no-wrap"}, sortable: true},
  {title: "Projectnummer Exact", value: "project_number_exact", cellProps: {class: "no-wrap"}, sortable: true},
  {title: "Datums", value: "event_count", cellProps: {class: "no-wrap"}, sortable: true, sort: sortNullLast},
  {title: "Start", value: "start", cellProps: {class: "no-wrap"}, sortable: true, sort: sortNullLast},
  {title: "Eind", value: "end", cellProps: {class: "no-wrap"}, sortable: true, sort: sortNullLast},
  {title: "Materiaal laden", value: "material_load", cellProps: {class: "no-wrap"}, sortable: true, sort: sortNullLast},
  {title: "Status", value: "status", align: "center", cellProps: {class: "no-wrap"}, sortable: true, sort: sortStatus},

  // Hidden columns for search.
  {value: "address", align: ' d-none' },
  {value: "remark", align: ' d-none' },

  {title: "Acties", value: "actions", cellProps: {class: "no-wrap center"}},
];

const editProject = (project) => {
  currentProject.value = project;
  showEditProjectDialog.value = true;
}

const newProject = () => {
  currentProject.value = {
    events: []
  };
  showEditProjectDialog.value = true;
}

const updateProjects = () => {
  loading.value = true;
  api
    .get("projectList?archive=" + (archive.value ? 'true' : 'false'))
    .then((response) => {
      loading.value = false;
      projects.value = response.data.data;
    })
    .catch((error) => {
      loading.value = false;
      errors.value = error.response.data.data
    });
}

const readonly = computed(() => !authStore.hasPermission('edit'));
</script>

<template>
  <v-card>
    <v-card-title class="space-between pt-4 pb-4">
      <span>
        {{ archive ? 'Gearchiveerde projecten' : 'Actieve projecten' }}
        <v-icon
          icon="mdi-refresh"
          size="small"
          color="primary"
          @click="updateProjects"
        />
      </span>
      <div class="d-flex align-center ga-4">
        <search-box
          v-model="search"
        />
        <v-icon
          :title="archive ? 'Toon actieve projecten' : 'Toon gearchiveerde projecten'"
          size="small"
          @click="archive = !archive; updateProjects()"
        >
          {{ archive ? 'mdi-archive-off' : 'mdi-archive' }}
        </v-icon>
        <v-btn
          v-if="!readonly"
          color="primary"
          prepend-icon="mdi-plus"
          @click="newProject"
        >
          Nieuw project
        </v-btn>
      </div>
    </v-card-title>
    <v-divider />
    <v-card-text>
      <v-data-table
        :headers="projectHeader"
        :items="projects"
        item-key="id"
        density="compact"
        :loading="loading"
        must-sort
        :sort-by="[{key: 'project_number', order: 'asc'}]"
        :search="search"
        class="stickyFirstColumn stickyLastColumn rowHover"
      >
        <template #item.name="{ item }">
          <span>
            <v-icon
              v-if="item.plannable === plannable.NOT_PLANNABLE_INTERNAL"
              color="indufastPurple"
              title="Dit project heeft dubbel ingeplande medewerkers"
            >
              mdi-alert-circle-outline
            </v-icon>
            <v-icon
              v-else-if="item.plannable === plannable.NOT_PLANNABLE_EXTERNAL"
              color="indufastRed"
              title="Dit project heeft medewerkers die niet meer beschikbaar zijn op dit moment"
            >
              mdi-alert-circle-outline
            </v-icon>
            {{ item.name }}
          </span>
        </template>
        <template #item.material_load="{ item }">
          <v-icon
            v-if="!item.material_load"
            color="indufastRed"
            icon="mdi-close"
          />
          {{ materialLoadTypes.find(material_load => material_load.value === item.material_load)?.title }}
        </template>
        <template #item.status="{ item }">
          <v-chip
            v-if="item.status"
            :color="projectStatus.find(type => type.value === item.status)?.color"
            variant="flat"
            size="small"
          >
            {{ projectStatus.find(type => type.value === item.status)?.title }}
          </v-chip>
        </template>
        <template #item.start="{ item }">
          {{ $filters.ucFirst($filters.formatDate(item.start)) }}
        </template>
        <template #item.end="{ item }">
          {{ $filters.ucFirst($filters.formatDate(item.end)) }}
        </template>
        <template #item.actions="{ item }">
          <v-icon
            color="primary"
            @click="editProject(item)"
          >
            {{ !readonly ? 'mdi-pencil' : 'mdi-eye' }}
          </v-icon>
        </template>
      </v-data-table>

      <project-edit-dialog
        v-model="showEditProjectDialog"
        :project-data="currentProject"
        :readonly="readonly"
        @saved="updateProjects"
      />
    </v-card-text>
  </v-card>
</template>
