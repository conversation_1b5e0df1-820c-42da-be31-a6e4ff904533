<script setup>
import {onMounted, ref} from "vue";
import {useTitle} from 'vue-page-title';
import {useRouter} from 'vue-router';
import createApiService from '@/services/api';
import {definePage} from "unplugin-vue-router/runtime";
import {useSnackbarStore} from "@/stores/snackbar.js";
import ExportDialog from "@/components/time_registration/exportDialog.vue";

definePage({
  meta: {
    title: 'Tijdsregistratie - Overzicht',
  },
})

const {title} = useTitle();
const router = useRouter();
const api = createApiService(router);
const currentYear = new Date().getFullYear();
const currentMonth = new Date().getMonth() + 1;
const snackbarStore = useSnackbarStore();
const showExportDialog = ref(false);
const employees = ref([]);
const loading = ref(false);
const search = ref('');
const archive = ref(false);

const employeeHeader = [
  {title: "Naam", value: "name", sortable: true, nowrap: true},
  {title: "Naam in Accredis", value: "name_accredis", sortable: true, nowrap: true},
  {title: "Open dagen", value: "newWorkdayCount", cellProps: {class: "no-wrap"}, sortable: true, nowrap: true},
  {title: "Actief", value: "active", cellProps: {class: "no-wrap"}, sortable: true, nowrap: true},
  {title: "Acties", value: "actions", cellProps: {class: "no-wrap center"}},
];

const fetchEmployees = async () => {
  loading.value = true;
  try {
    const response = await api.get("employeeList?" + new URLSearchParams({
      accredis: true,
      archive: archive.value ? 'true' : 'false',
    }).toString());

    employees.value = response.data.data;
  } catch (error) {
    snackbarStore.showMessage("Onbekende fout bij het ophalen van de medewerkers.", "error");
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const openDetails = (event, row) => {
  router.push({
    name: 'time-registration-details',
    params: { employee: row.item.id, year: currentYear, month: currentMonth }
  });
};

onMounted(() => {
  fetchEmployees();
});

</script>

<template>
  <export-dialog v-model="showExportDialog" />

  <v-card>
    <v-card-title class="space-between pt-4 pb-4">
      <span>
        {{ archive ? 'Niet-actieve medewerkers' : 'Actieve medewerkers' }}
        <v-icon
          icon="mdi-refresh"
          size="small"
          color="primary"
          @click="fetchEmployees"
        />
      </span>
      <div class="d-flex align-center ga-4">
        <v-icon
          :title="archive ? 'Toon actieve medewerkers' : 'Toon gearchiveerde medewerkers'"
          size="small"
          @click="archive = !archive; fetchEmployees()"
        >
          {{ archive ? 'mdi-archive-off' : 'mdi-archive' }}
        </v-icon>
        <v-btn
          prepend-icon="mdi-download"
          color="primary"
          text="Download"
          @click="showExportDialog = true"
        />
      </div>
    </v-card-title>
    <v-divider />
    <v-card-text>
      <v-data-table
        :headers="employeeHeader"
        :items="employees"
        item-key="id"
        density="compact"
        :loading="loading"
        must-sort
        :sort-by="[{key: 'name', order: 'asc'}]"
        :search="search"
        class="stickyFirstColumn stickyLastColumn rowHover"
        @click:row="openDetails"
      >
        <template #item.name="{ item }">
          <div class="d-flex align-center ga-2">
            <span>{{ item.name }}</span>
            <span
              v-if="!archive && !item.active && item.newWorkdayCount"
              :title="'Niet-actieve medewerker met ' + item.newWorkdayCount + ' openstaande ' + (item.newWorkdayCount === 1 ? 'dag' : 'dagen')"
            >
              <v-icon
                icon="mdi-clock-alert-outline"
                size="small"
                color="warning"
              />
            </span>
            <span
              v-if="!archive && !item.active && item.may_login && !item.newWorkdayCount"
              title="Niet-actieve medewerker die in mag loggen"
            >
              <v-icon
                icon="mdi-account-alert-outline"
                size="small"
                color="warning"
              />
            </span>
          </div>
        </template>
        <template #item.active="{ item }">
          <v-icon :color="item.active ? 'indufastGreen' : 'indufastRed'">
            {{ item.active ? 'mdi-check' : 'mdi-close' }}
          </v-icon>
        </template>
        <template #item.actions="{ item }">
          <router-link
            :to="{ name: 'time-registration-details', params: { employee: item.id, year: currentYear, month: currentMonth } }"
          >
            <v-icon
              color="primary"
              icon="mdi-eye"
            />
          </router-link>
        </template>
      </v-data-table>
    </v-card-text>
  </v-card>
</template>
