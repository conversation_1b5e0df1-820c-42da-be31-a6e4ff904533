<script setup>
import {useSnackbarStore} from '@/stores/snackbar';

const snackbarStore = useSnackbarStore();
</script>

<template>
  <v-app>
    <router-view />
    <v-snackbar
      v-model="snackbarStore.visible"
      :color="snackbarStore.type"
      timeout="-1"
    >
      {{ snackbarStore.message }}
      <template #actions>
        <v-btn
          color="white"
          icon
          size="small"
          @click="snackbarStore.hideMessage()"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </template>
    </v-snackbar>
  </v-app>
</template>

<style lang="scss">
body {
  font-family: Poppins, sans-serif;
}

.v-overlay-container {
  a,
  a:visited {
    color: rgb(var(--v-theme-secondary));
  }

  a:hover,
  a:focus,
  a:active {
    color: rgb(var(--v-theme-primary));
  }
}

.required label.v-label.v-field-label:after {
  content: " *";
  color: red;
}

th.no-wrap,
td.no-wrap {
  white-space: nowrap;
  width: 0;
}

td.center {
  text-align: center;
}

main.v-main {
  background-color: rgb(var(--v-theme-GSDBackground));
}

.stickyLastColumn td:last-child,
.stickyLastColumn th:last-child {
  position: sticky;
  right: 0;
  z-index: 100;
  background-color: rgb(var(--v-theme-surface));
}

.rowHover tr:hover td {
  background-color: #F5F5F5;
}

.stickyFirstColumn td:first-child,
.stickyFirstColumn th:first-child {
  position: sticky;
  left: 0;
  z-index: 100;
  background-color: rgb(var(--v-theme-surface));
}

.space-between {
  display: flex !important;
  justify-content: space-between;
}

.markup li {
  margin-left: 20px;
}

.markup p {
  margin: 12px 0;
}
</style>
