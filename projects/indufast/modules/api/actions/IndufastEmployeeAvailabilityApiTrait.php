<?php

  use classes\ApiResponse;
  use domain\project\service\ProjectUpdater;

  trait IndufastEmployeeAvailabilityApiTrait {
    use PropertyCastTrait;

    public function executeEmployeeAvailabilityList(): void {
      $rules = [
        'date' => 'required|date:Y-m-d',
      ];
      $data = $this->validateData($_GET, $rules)->getValidatedData();

      $result = [];
      try {
        $employees = IndufastEmployee::find_all_by(['active' => true]);
        $eventsByEmployee = IndufastEmployee::getCalendarEventsForEmployees($employees, $data['date']);

        foreach ($employees as $employee) {
          $events = $eventsByEmployee[$employee->id] ?? [];
          if ($employee->isNonWorkingDay($data['date'])) {
            $availability = IndufastEmployee::NOT_AVAILABLE_NON_WORKING_DAY;
          }
          else {
            $availability = $employee->calculateAvailabilityFromEvents($events, $data['date'], true);
          }

          $result[] = [
            'employee'     => $employee,
            'availability' => $availability,
            'events'       => $events,
          ];
        }
      } catch (\Exception $e) {
        ApiResponse::sendResponseError($e->getMessage());
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $result);
    }

    public function executeEmployeeAvailabilityUpdate(): void {
      try {
        // Updates employee availability and project validity
        ProjectUpdater::updateProjectValidity();
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK);
      } catch (\Exception $e) {
        ApiResponse::sendResponseError($e->getMessage());
      }
    }

    public function executeEmployeeAvailabilityForProject(): void {
      $rules = [
        'employee_id' => 'required|integer',
        'project_id' => 'required|integer',
        'exclude_event_id' => 'integer',
      ];
      $data = $this->validateData($_GET, $rules)->getValidatedData();

      try {
        $project = IndufastProject::find_by_id($data['project_id']);
        if (!$project) {
          throw new \Exception('Project not found');
        }

        $employee = IndufastEmployee::find_by_id($data['employee_id']);
        if (!$employee) {
          throw new \Exception('Employee not found');
        }

        $events = [];
        foreach ($project->events() as $event) {
          if ($data['exclude_event_id'] && $event->id == $data['exclude_event_id']) {
            continue;
          }
          if ($event->isPastEvent()) {
            continue;
          }

          $date = date('Y-m-d', strtotime($event->start));
          $availability = $employee->getAvailability($date, eventToIgnore: $event);
          $events[] = [
            'event' => $event,
            'availability' => $availability,
            'conflict' => $employee->hasConflict($event, $availability),
          ];
        }

      } catch (\Exception $e) {
        ApiResponse::sendResponseError($e->getMessage());
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $events);
    }

    public function executeEmployeeAvailabilityRange(): void {
      $rules = [
        'date' => 'required|date:Y-m-d',
        'view' => 'required|string|in:week,month',
      ];
      $validation = $this->validateData($_GET, $rules);
      $data = $validation->getValidatedData();

      ['start' => $calculatedStart, 'end' => $end] = $this->getStartAndEndDate($data['date'], $data['view']);
      $tomorrow = date('Y-m-d', strtotime('+1 day'));
      $start = ($calculatedStart > $tomorrow) ? $calculatedStart : $tomorrow; // Use calculated start if it's after tomorrow, otherwise use tomorrow

      // If the range end is in the past, return empty response
      if ($end < date('Y-m-d')) {
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK);
      }

      $result = [];
      try {
        // Exclude non-active & E ranks
        $employees = IndufastEmployee::find_all_by(['active' => true], 'AND rank != "E"');
        // Get all calendar events for all employees in the specified date range
        $eventsByEmployee = IndufastEmployee::getCalendarEventsForEmployees($employees, $start, $end);

        // Generate availability for each day in the range
        $currentDate = new DateTime($start);
        $endDate = new DateTime($end);

        while ($currentDate <= $endDate) {
          $dateString = $currentDate->format('Y-m-d');

          // Collect availability data for this day
          $employeeDetails = [];

          foreach ($employees as $employee) {
            $employeeEvents = $eventsByEmployee[$employee->id] ?? [];
            $dayEvents = $this->filterEventsForDate($employeeEvents, $dateString);

            $availability = $employee->calculateAvailabilityFromEvents($dayEvents, $dateString);

            $employeeDetails[] = [
              'name' => $employee->name ?? $employee->email ?? 'Onbekend',
              'availability' => $availability,
            ];
          }

          // Build the data object for this day (raw data for front-end formatting)
          $result[] = [
            'date' => $dateString,
            'employee_details' => $employeeDetails,
            'type' => 'availability',
          ];

          $currentDate->modify('+1 day');
        }
      } catch (\Exception $e) {
        ApiResponse::sendResponseError($e->getMessage());
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $result);
    }

    private function filterEventsForDate(array $events, string $dateString): array {
      return array_filter($events, function ($event) use ($dateString) {
        if (empty($event->start) || empty($event->end)) return false;

        $eventStart = $event->start->dateTime ?? $event->start->date ?? null;
        $eventEnd = $event->end->dateTime ?? $event->end->date ?? null;

        if (!$eventStart || !$eventEnd) return false;

        $eventStartDate = date('Y-m-d', strtotime($eventStart));
        $eventEndDate = date('Y-m-d', strtotime($eventEnd));

        // For all-day events (date only, no dateTime), subtract 1 day from end date
        // Google Calendar sets end date to the day after for all-day events
        if (isset($event->end->date) && !isset($event->end->dateTime)) {
          $eventEndDate = date('Y-m-d', strtotime($eventEndDate . ' -1 day'));
        }

        // Check if the current date falls within the event's date range
        return $dateString >= $eventStartDate && $dateString <= $eventEndDate;
      });
    }
  }