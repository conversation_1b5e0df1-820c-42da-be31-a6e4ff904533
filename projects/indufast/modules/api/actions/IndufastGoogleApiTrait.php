<?php

  use classes\ApiResponse;
  use domain\google\service\GoogleMapsService;

  trait IndufastGoogleApiTrait {
    use PropertyCastTrait;

    public function executeSearchLocations(): void {
      $rules = [
        'query' => 'required|string',
      ];
      $data = $this->validateData($_GET, $rules)->getValidatedData();

      $service = new GoogleMapsService();
      $locations = $service->getLocations($data['query']);

      if (isset($locations['error_message'])) {
        ApiResponse::sendResponseError('Google Maps API error: ' . $locations['error_message']);
      } else {
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $locations['predictions'] ?? []);
      }
    }

    public function executeGoogleEventList(): void {
      $rules = [
        'date' => 'required|date:Y-m-d',
        'view' => 'required|string|in:week,month',
      ];
      $validation = $this->validateData($_GET, $rules);
      $data = $validation->getValidatedData();

      ['start' => $start, 'end' => $end] = $this->getStartAndEndDate($data['date'], $data['view']);

      $events = IndufastCalendarEvent::list($start, $end);
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $events);
    }

  }