<?php

  namespace domain\google\service;

  use Config;
  use DateTimeInterface;
  use Google\Client;
  use Google\Service\Calendar;
  use Google\Service\Calendar\Event;
  use Google\Service\Calendar\EventAttendee;
  use Google\Service\Calendar\EventDateTime;
  use Google\Service\Calendar\EventReminders;
  use Google\Service\Exception;
  use IndufastCalendarEvent;
  use IndufastGoogleDriveFile;
  use IndufastProject;
  use Psr\Http\Message\RequestInterface;

  class GoogleCalendarService {

    protected Calendar $calendarService;

    /**
     * @throws \Exception
     */
    public function __construct() {
      $client = new Client();
      $client->setAuthConfig(Config::get('GOOGLE_API_AUTH_CONFIG'));
      $client->setSubject(Config::get('GOOGLE_API_SUBJECT'));
      $client->setAccessType('offline');
      $client->addScope(Calendar::CALENDAR);

      $this->calendarService = new Calendar($client);
    }

    /**
     * @throws \Exception
     */
    public function deleteGoogleCalendarEvent(\IndufastCalendarEvent $event): void {
      try {
        $options = ['sendUpdates' => 'none'];
        $this->calendarService->events->delete(Config::get('GOOGLE_API_CALENDAR_ID'), $event->google_calendar_event_id, $options);
      }
      catch (\Exception $e) {
        // Event no longer exists, ignore.
        if ($e->getCode() !== 410) {
          throw $e;
        }
      }
      $event->google_calendar_event_id = null;
    }

    public function deleteGoogleCalendarEventsBatch(array $indufastEvents): void {
      $client = $this->calendarService->getClient();
      $client->setUseBatch(true);
      $batch =  $this->calendarService->createBatch();
      $options = ['sendUpdates' => 'none'];
      foreach ($indufastEvents as $event) {
        /** @var RequestInterface $request */
        $request = $this->calendarService->events->delete(Config::get('GOOGLE_API_CALENDAR_ID'), $event->google_calendar_event_id, $options);
        $batch->add($request, $event->id);
      }
      $batch->execute();
      $client->setUseBatch(false);
    }

    public function getGoogleCalendarEventsBatch(array $calendarIds, string $dateStart, ?string $dateEnd = null): array
    {
      $start = new \DateTime($dateStart . ' 00:00:00');
      $end = new \DateTime(($dateEnd ?? $dateStart) . ' 23:59:59');

      $client = $this->calendarService->getClient();
      $client->setUseBatch(true);

      $batch =  $this->calendarService->createBatch();

      $optParams = [
        'timeMin'               => $start->format(DateTimeInterface::RFC3339),
        'timeMax'               => $end->format(DateTimeInterface::RFC3339),
        'timeZone'              => 'Europe/Amsterdam',
        'showHiddenInvitations' => true,
        'singleEvents'          => true,
        'fields'                => 'items/id,items/start,items/end,items/summary,items/description,items/location,items/visibility,items/transparency,items/colorId,items/attendees,items/status',
      ];

      foreach ($calendarIds as $calendarId) {
        /** @var RequestInterface $request */
        $request = $this->calendarService->events->listEvents($calendarId, $optParams);
        $batch->add($request, $calendarId);
      }

      $results = $batch->execute();
      $client->setUseBatch(false);

      // Group results by calendar ID
      $eventsByCalendar = [];
      foreach ($results as $key => $events) {
        // The key is 'response-<calendar-id>' so we remove the prefix
        $calendarId = str_starts_with($key, 'response-') ? substr($key, strlen('response-')) : $key;
        $eventsByCalendar[$calendarId] = $events;
      }
      return $eventsByCalendar;
    }

    public function getGoogleEventFilesBatch(array $indufastEvents): array {
      $client = $this->calendarService->getClient();
      $client->setUseBatch(true);
      $batch =  $this->calendarService->createBatch();
      $optParams = ['fields' => 'attachments'];
      foreach ($indufastEvents as $indufastEvent) {
        // cast to object if not already
        $indufastEvent = is_object($indufastEvent) ? $indufastEvent : (object)$indufastEvent;

        /** @var RequestInterface $request */
        $request = $this->calendarService->events->get(Config::get('GOOGLE_API_CALENDAR_ID'), $indufastEvent->google_calendar_event_id, $optParams);
        $batch->add($request, $indufastEvent->id);
      }
      $results = $batch->execute();
      $client->setUseBatch(false);

      // the key is 'response-<event-id>' so we remove the prefix
      $filesByEvent = [];
      foreach ($results as $key => $event) {
        $eventId = str_starts_with($key, 'response-') ? substr($key, strlen('response-')) : $key;
        $filesByEvent[$eventId] = $event->getAttachments() ?? [];
      }

      return $filesByEvent;
    }

    public function updateGoogleCalendarEventsBatch(array $indufastEvents): void {
      if (empty($indufastEvents)) return;

      $client = $this->calendarService->getClient();
      $client->setUseBatch(true);

      // Get existing events
      $existingEvents = $this->getExistingGoogleEvents($indufastEvents);

      // Create and execute update/insert batch
      $this->executeUpdateInsertBatch($indufastEvents, $existingEvents);

      $client->setUseBatch(false);
    }


    private function getExistingGoogleEvents(array $indufastEvents): array {
      $batch = $this->calendarService->createBatch();
      $getRequests = [];

      foreach ($indufastEvents as $event) {
        if ($event->google_calendar_event_id) {
          /** @var RequestInterface $request */
          $request = $this->calendarService->events->get(Config::get('GOOGLE_API_CALENDAR_ID'), $event->google_calendar_event_id);
          $batch->add($request, 'get-' . $event->id);
          $getRequests[$event->id] = $event;
        }
      }
      if (empty($getRequests)) return [];

      $getResults = $batch->execute();
      $existingEvents = [];

      foreach ($getResults as $key => $result) {
        $eventId = str_starts_with($key, 'response-get-') ? substr($key, strlen('response-get-')) : $key;
        if (!($result instanceof \Google_Service_Exception) || $result->getCode() !== 404) {
          $existingEvents[$eventId] = $result;
        }
      }

      return $existingEvents;
    }

    private function executeUpdateInsertBatch(array $indufastEvents, array $existingEvents): void {
      $batch = $this->calendarService->createBatch();
      $options = ['sendUpdates' => 'none', 'supportsAttachments' => true];
      $calendarId = Config::get('GOOGLE_API_CALENDAR_ID');

      foreach ($indufastEvents as $event) {
        $googleEvent = $this->prepareGoogleEvent($event, $existingEvents);

        if ($googleEvent->getId()) {
          /** @var RequestInterface $request */
          $request = $this->calendarService->events->update($calendarId, $event->google_calendar_event_id, $googleEvent, $options);
          $batch->add($request, 'update-' . $event->id);
        } else {
          /** @var RequestInterface $request */
          $request = $this->calendarService->events->insert($calendarId, $googleEvent, $options);
          $batch->add($request, 'insert-' . $event->id);
        }
      }

      $results = $batch->execute();

      // Update google_calendar_event_id for newly created events
      foreach ($results as $key => $result) {
        if (!str_starts_with($key, 'response-insert-')) continue;

        $eventId = substr($key, strlen('response-insert-'));
        $event = array_find($indufastEvents, fn($e) => $e->id == $eventId);
        if (!$event) continue;

        $event->google_calendar_event_id = $result->getId();
      }
    }

    private function prepareGoogleEvent(\IndufastCalendarEvent $event, array $existingEvents): Event {
      $project = $event->project();

      $start = new EventDateTime();
      $start->setDateTime(new \DateTime($event->start)->format(DateTimeInterface::RFC3339));

      $end = new EventDateTime();
      $end->setDateTime(new \DateTime($event->end)->format(DateTimeInterface::RFC3339));

      // Use existing event if found, otherwise create new
      $googleEvent = $existingEvents[$event->id] ?? new Event();

      $googleEvent->setGuestsCanInviteOthers(false);
      $googleEvent->setReminders(new EventReminders(['useDefault' => true]));
      $googleEvent->setStart($start);
      $googleEvent->setEnd($end);
      $googleEvent->setLocation($project->address);
      $googleEvent->setStatus('confirmed');

      if ($event->type === IndufastCalendarEvent::TYPE_BLAST) {
        $googleEvent->setSummary('Straaldag: ' . $project->name . ($event->confirmed ? '' : ' (niet bevestigd)'));
        $googleEvent->setColorId(IndufastProject::COLOR_ID_BANANA);
        $googleEvent->setDescription($event->remark);
      } else {
        $googleEvent->setSummary($project->name);
        $googleEvent->setColorId($project->getColorId());
        $googleEvent->setDescription($project->getCalenderDescription($event));
      }

      $attendees = [];
      foreach ($event->employees() as $eventEmployee) {
        $attendee = new EventAttendee();
        $attendee->setEmail($eventEmployee->employee->email);
        $attendees[] = $attendee;
      }
      $googleEvent->setAttendees($attendees);

      if ($event->type === IndufastCalendarEvent::TYPE_WORK) {
        $files = IndufastGoogleDriveFile::find_all_by(['calendar_event_id' => $event->id]);
        $attachments = [];
        foreach ($files as $file) {
          $attachments[] = [
            'fileId'   => $file->id,
            'fileUrl'  => $file->url,
            'title'    => $file->name,
            'iconLink' => $file->icon_url,
          ];
        }
        $googleEvent->setAttachments($attachments);
      }
      return $googleEvent;
    }
  }
