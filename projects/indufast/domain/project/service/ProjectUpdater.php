<?php

  namespace domain\project\service;

  use DBConn;
  use IndufastCalendarEvent;
  use IndufastCalendarEventEmployee;
  use IndufastEmployee;
  use stdClass;

  class ProjectUpdater {

    /**
     * @throws \GsdDbException
     * @throws \GsdException
     * @throws \Exception
     */
    public static function updateProjectValidity(): void {
      // We only want to check for one month ahead
      $today = date('Y-m-d');
      $oneMonthAhead = date('Y-m-d', strtotime('+1 month'));

      $employees = IndufastEmployee::find_all();
      $activeEmployees = array_filter($employees, fn($e) => $e->active == 1);
      $eventsByEmployee = IndufastEmployee::getCalendarEventsForEmployees($activeEmployees, $today, $oneMonthAhead); // active only
      $indufastEvents = IndufastCalendarEvent::find_all("WHERE start >= '$today 0:00:00' AND start < '$oneMonthAhead 0:00:00' ORDER BY start");

      foreach ($indufastEvents as $indufastEvent) {
        $indufastEvent->dayPart();

        $eventEmployees = $indufastEvent->employees();
        if (empty($eventEmployees)) continue;

        foreach ($eventEmployees as $eventEmployee) {
          $conflict = null;
          /** @var IndufastEmployee $employee */
          $employee = array_find($employees, fn($e) => $e->id === $eventEmployee->employee_id);

          if (!$employee) {
            logToFile(__FUNCTION__, "Employee not found: " . var_export($eventEmployee, 1));
            $conflict = new stdClass();
            $conflict->employee_id = $eventEmployee->employee_id;
            $conflict->event_id = $eventEmployee->calendar_event_id;
            $conflict->reason = 'employee_not_found';
          } else {
            if ($employee->active != 1) {
              $conflict = new stdClass();
              $conflict->employee_id = $employee->id;
              $conflict->event_id = $eventEmployee->calendar_event_id;
              $conflict->reason = 'employee_inactive';
            } else {
              $date = date('Y-m-d', strtotime($indufastEvent->start));
              $eventsForEmployee = $eventsByEmployee[$employee->id] ?? [];
              $eventsForEmployee = self::filterGoogleCalendarEventsForSpecificDate($eventsForEmployee, $date);
              $availability = $employee->calculateAvailabilityFromEvents($eventsForEmployee, $date, false, $indufastEvent);

              if ($employee->hasConflict($indufastEvent, $availability)) {
                $conflict = new stdClass();
                $conflict->employee_id = $employee->id;
                $conflict->event_id = $eventEmployee->calendar_event_id;

                // Check if conflict is external (caused by events not in our database)
                $availabilityIgnoringInternal = $employee->calculateAvailabilityFromEvents($eventsForEmployee, $date, true, $indufastEvent);
                $externalConflict = $employee->hasConflict($indufastEvent, $availabilityIgnoringInternal);

                $conflict->reason = $availability;
                $conflict->internal = !$externalConflict;
              }
            }
          }

          if ($conflict) {
            logToFile(__CLASS__, "Conflict for employee ID {$eventEmployee->employee_id} in event ID {$indufastEvent->id}: " . json_encode($conflict));
            $eventEmployee->conflict = json_encode($conflict);
          } else {
            logToFile(__CLASS__, "No conflicts for employee ID {$eventEmployee->employee_id} in event ID {$indufastEvent->id}.");
            $eventEmployee->conflict = null;
          }

          $eventEmployee->from_db = true;
          $eventEmployee->save();
        }
      }
    }

    public static function cleanupPastEvents(): void {
      $event = IndufastCalendarEvent::getTablename();
      $eventEmployee = IndufastCalendarEventEmployee::getTablename();

      // update all events from before yesterday
      $yesterday = date('Y-m-d 0:00:00', strtotime('-1 day'));
      $query = <<<SQL
        UPDATE $eventEmployee
        SET conflict = NULL
        WHERE calendar_event_id IN (
          SELECT id FROM $event
          WHERE start < '$yesterday'
        )
      SQL;
      DBConn::db_link()->query($query);
    }

    public static function filterGoogleCalendarEventsForSpecificDate(array $events, string $date): array {
      return array_filter($events, function($event) use ($date) {
        if ($event->getStart()->getDate()) {
          return $event->getStart()->getDate() === $date;
        } elseif ($event->getStart()->getDateTime()) {
          return date('Y-m-d', strtotime($event->getStart()->getDateTime())) === $date;
        }
        return false;
      });
    }
  }