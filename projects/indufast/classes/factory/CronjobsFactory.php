<?php

  use domain\project\service\ProjectUpdater;

  class CronjobsFactory extends Cronjobs {

    const int UPDATE_PROJECT_VALIDITY_INTERVAL_MINUTES = 5;

    public static function execute() {
      CronjobsFactory::backupMysql();
      JobQueue::executeJobs();

      // custom
      CronjobsFactory::updateProjectValidity();
      CronjobsFactory::cleanupPastEvents();

      CronjobsFactory::executeCleanDirTemp();
      CronjobsFactory::cleanLogDir();
    }

    /**
     * Check if there are planning conflicts.
     *
     * @throws \GsdDbException
     * @throws \GsdException
     * @throws \Exception
     */
    public static function updateProjectValidity(): void {
      if (ENVIRONMENT == 'LOCAL' || date('i') % self::UPDATE_PROJECT_VALIDITY_INTERVAL_MINUTES == 0) {
        logToFile("cron", sprintf('%s started', __FUNCTION__));
        ProjectUpdater::updateProjectValidity();
        logToFile("cron", sprintf('%s done', __FUNCTION__));
      }
    }

    /**
     * Cleans up past events in the project.
     * This method is scheduled to run at midnight every day.
     */
    public static function cleanupPastEvents(): void {
      if (ENVIRONMENT == 'LOCAL' || (date('H') == 0 && date('i') == 0)) { // Runs at midnight every day
        logToFile("cron", sprintf('%s started', __FUNCTION__));
        ProjectUpdater::cleanupPastEvents();
        logToFile("cron", sprintf('%s done', __FUNCTION__));
      }
    }
  }