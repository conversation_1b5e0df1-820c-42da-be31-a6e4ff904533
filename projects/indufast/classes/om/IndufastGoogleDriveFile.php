<?php

AppModel::loadModelClass('IndufastGoogleDriveFileModel');

class IndufastGoogleDriveFile extends IndufastGoogleDriveFileModel {
  use ModelFillTrait;
  use ValidationTrait;
  use PropertyCastTrait {
    castProperties as castPropertiesTrait;
  }

  const CAST_PROPERTIES = [
    'id'                => 'int',
    'calendar_event_id' => 'int',
    'from_db'           => 'hidden',
  ];

  protected array $fillable = [
    'name'              => 'string|required|max:255',
    'url'               => 'string|required|max:255',
    'icon_url'          => 'string|required|max:255',
    'calendar_event_id' => 'integer|exists:indufast_calendar_event,id',
  ];
}