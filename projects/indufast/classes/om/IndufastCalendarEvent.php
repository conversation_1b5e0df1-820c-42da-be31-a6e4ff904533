<?php

  use domain\google\service\GoogleCalendarService;
  use Google\Service\Calendar\Event;

  AppModel::loadModelClass('IndufastCalendarEventModel');

  class IndufastCalendarEvent extends IndufastCalendarEventModel {

    const DAY_PART_MORNING = 'morning';
    const DAY_PART_AFTERNOON = 'afternoon';
    const DAY_PART_WHOLE_DAY = 'whole_day';

    const string TYPE_BLAST = 'blast';
    const string TYPE_WORK = 'work';

    use PropertyCastTrait {
      castProperties as castPropertiesTrait;
    }
    use ModelFillTrait;
    use ValidationTrait;

    public array $employees = [];
    public array $employee_ids = [];
    public string $day_part = '';
    public ?IndufastEmployee $team_lead = null;
    public ?array $material_loaders = null;
    public array $google_drive_files = [];

    protected array $fillable = [
      'project_id'                 => 'required|integer|exists:indufast_project,id',
      'employee_ids'               => 'array|prohibited_unless:type,work',
      'employee_ids.*'             => 'integer|exists:indufast_employee,id',
      'team_lead_employee_id'      => 'integer',
      'material_load_employee_ids' => 'array',
      'start'                      => 'required|date:Y-m-d G:i:s|range:end,asc|project_same_day:{id}|today_or_later',
      'end'                        => 'required|date:Y-m-d H:i:s|range:start,desc|today_or_later',
      'confirmed'                  => 'boolean',
      'remark'                     => 'nullable|string',
      'fte'                        => 'nullable|integer|min:1|max:100',
      'type'                       => 'required|in:work,blast',
      'google_drive_files'         => 'array',
    ];

    const array CAST_PROPERTIES = [
      'id'                         => 'int',
      'project_id'                 => 'int',
      'team_lead_employee_id'      => 'int',
      'fte'                        => 'int',
      'confirmed'                  => 'boolean',
      'material_load_employee_ids' => 'array',
      'from_db'                    => 'hidden',
      'employee_ids'               => 'hidden',
      'google_drive_files'         => 'hidden',
    ];

    public function castProperties(): void {
      $this->castPropertiesTrait();
      $this->employees();
      $this->dayPart();
      $this->teamLead();
      $this->materialLoaders();
    }

    /**
     * Update the Google Calendar event when saving a project with start and end set.
     *
     * @throws GsdDbException
     * @throws GsdException
     */
    public function save(array &$errors = []): mysqli_result|bool {
      $this->saveGoogleDriveFiles();
      $this->castPropertiesForSave();
      return parent::save($errors);
    }

    public static function batchSave(array $events): void {
      new GoogleCalendarService()->updateGoogleCalendarEventsBatch($events);

      foreach ($events as $event) {
        $event->saveGoogleDriveFiles();
        $event->castPropertiesForSave();
        $event->save();
      }
    }

    public function destroy(): mysqli_result|bool {
      try {
        new GoogleCalendarService()->deleteGoogleCalendarEvent($this);
      } catch (Exception $e) {
        if ($e->getCode() !== 404) {
          logToFile(__CLASS__, var_export($e->getMessage(), true));
          throw $e;
        }
      }

      return parent::destroy();
    }

    private function saveGoogleDriveFiles(): void {
      if (!isset($this->google_drive_files)) return;

      // cast to objects if not already
      $this->google_drive_files = array_map(fn($file) => is_object($file) ? $file : (object)$file, $this->google_drive_files);

      $existingFiles = IndufastGoogleDriveFile::find_all_by(['calendar_event_id' => $this->id]);
      $existingIds = array_column($existingFiles, 'id');
      $submittedIds = array_column($this->google_drive_files, 'id');
      $filesToAdd = array_filter($this->google_drive_files, fn($file) => empty($file->id) || !in_array($file->id, $existingIds));

      foreach ($filesToAdd as $file) {
        $fileToSave = new IndufastGoogleDriveFile();
        $fileToSave->fill([
            'name' => $file->name,
            'url' => $file->url,
            'icon_url' => $file->icon_url,
            'calendar_event_id' => $this->id,
        ]);
        $fileToSave->castPropertiesForSave();
        $fileToSave->save();
      }
      $filesToDelete = array_filter($existingFiles, fn($existingFile) => !in_array($existingFile->id, $submittedIds));
      foreach ($filesToDelete as $file) {
        $file->destroy();
      }
    }

    public function materialLoaders(): array {
      if ($this->material_loaders) {
        return $this->material_loaders;
      }
      $ids = $this->material_load_employee_ids ?? [];
      $this->material_loaders = array_map(fn ($id) => IndufastEmployee::find_by_id($id), $ids);

      return $this->material_loaders;
    }

    public function teamLead(): ?IndufastEmployee {
      if ($this->team_lead) {
        return $this->team_lead;
      }
      if ($employee = IndufastEmployee::find_by_id($this->team_lead_employee_id)) {
        return $this->team_lead = $employee;
      }
      return null;
    }

    public function project(): IndufastProject {
      if ($project = IndufastProject::find_by_id($this->project_id)) {
        return $project;
      }

      throw new Exception("Project not found");
    }

    /**
     * @return IndufastCalendarEventEmployee[]
     */
    public function employees(): array {
      $eventEmployees = IndufastCalendarEventEmployee::find_all_by(['calendar_event_id' => $this->id]);
      foreach ($eventEmployees as $eventEmployee) {
        $eventEmployee->load(['employee']);
      }

      usort($eventEmployees, fn ($a, $b) =>
        [$a->employee->rank, $a->employee->rank_number] <=> [$b->employee->rank, $b->employee->rank_number]
      );

      $this->employee_ids = array_column($eventEmployees, 'employee_id');
      return $this->employees = $eventEmployees;
    }

    /**
     * @throws Exception
     */
    public function dayPart(): void {
      $start = new DateTime($this->start);
      $end = new DateTime($this->end);

      $morningEnd = new DateTime($start->format('Y-m-d') . ' ' . IndufastEmployee::MORNING_END);
      $afternoonStart = new DateTime($start->format('Y-m-d') . ' ' . IndufastEmployee::AFTERNOON_START);

      if ($end <= $morningEnd) {
        $this->day_part = self::DAY_PART_MORNING;
      }
      elseif ($start >= $afternoonStart) {
        $this->day_part = self::DAY_PART_AFTERNOON;
      }
      else {
        $this->day_part = self::DAY_PART_WHOLE_DAY;
      }
    }

    public function updateEmployees(): void {
      $filter = (!empty($this->employee_ids)) ? 'AND employee_id NOT IN (' . implode(',', $this->employee_ids) . ')' : '';

      if (!empty($this->id)) {
        IndufastCalendarEventEmployee::delete_by(['calendar_event_id' => $this->id], $filter);
      }

      $current_employees_ids = array_column(IndufastCalendarEventEmployee::find_all_by(['calendar_event_id' => $this->id]), 'employee_id');
      $new_employees_ids = array_diff(array_unique($this->employee_ids), $current_employees_ids);

      foreach ($new_employees_ids as $employee_id) {
        $event_employee = new IndufastCalendarEventEmployee();
        $event_employee->calendar_event_id = $this->id;
        $event_employee->employee_id = $employee_id;
        $event_employee->save();
      }
    }

    public function isPastEvent(): bool {
      return new DateTime($this->start) < new DateTime('today');
    }

    static public function holidays(): array {
      $currentYear = (int) date('Y');
      $allHolidays = [];

      // Get holidays from 2024 until one year in the future
      for ($year = 2024; $year <= $currentYear + 1; $year++) {
        $dutchHolidays = DateTimeHelper::getDutchHolidays($year);

        // Remove Good Friday (not a working holiday in most CAOs)
        unset($dutchHolidays['goede_vrijdag']);

        // Liberation Day only applies every 5 years (2020, 2025, etc.)
        if ($year % 5 != 0) {
          unset($dutchHolidays['bevrijdingsdag']);
        }
        $allHolidays = array_merge($allHolidays, array_flip($dutchHolidays));
      }
      return $allHolidays;
    }

    static public function list(string $start, string $end): array {
      $events = [];
      try {
        $service = new GoogleCalendarService();
        $calendarId = Config::get('GOOGLE_API_CALENDAR_ID');
        $calendarEvents = $service->getGoogleCalendarEventsBatch([$calendarId], $start, $end);

        foreach ($calendarEvents as $calendar) {
          /** @var Event $event */
          foreach ($calendar->getItems() as $event) {
            $attendees = [];

            foreach ($event->getAttendees() as $attendee) {
              $attendees[] = [
                'email'  => $attendee->getEmail(),
                'status' => $attendee->getResponseStatus(),
              ];
            }

            if ($event->getStart()->getDateTime()) {
              $startDateTime = new DateTime($event->getStart()->getDateTime())->format('Y-m-d H:i');
              $endDateTime = new DateTime($event->getEnd()->getDateTime())->format('Y-m-d H:i');
            }
            else {
              $startDateTime = $event->getStart()->getDate();
              // Subtract one day from the end date to avoid all-day events showing as one day longer
              $endDateTime = date('Y-m-d', strtotime($event->getEnd()->getDate() . ' -1 day'));
            }

            $events[$event->getId()] = [
              'title'       => $event->getSummary(),
              'event_id'    => $event->getId(),
              'description' => $event->getDescription(),
              'color'       => $event->getColorId(),
              'location'    => $event->getLocation(),
              'attendees'   => $attendees,
              'start'       => $startDateTime,
              'end'         => $endDateTime,
              'project'     => null,
            ];
          }
        }

        self::addIndufastProjects($events);
        self::addIndufastEmployees($events);
      }
      catch (Exception $e) {
        if ($e->getCode() !== 404) {
          logToFile(__CLASS__, var_export($e->getMessage(), true));
          throw $e;
        }
      }

      return array_values($events);
    }

    static protected function addIndufastProjects(array &$events): void {
      $query = sprintf("
          SELECT ip.id, ice.google_calendar_event_id, ice.id as event_id FROM indufast_project AS ip
          JOIN indufast_calendar_event AS ice ON ip.id = ice.project_id
          WHERE %s", DbHelper::getSqlIn('ice.google_calendar_event_id', array_column($events, 'event_id')));
      $result = DBConn::db_link()->query($query);

      $projectEventMappings = [];
      while ($row = $result->fetch_assoc()) {
        $projectEventMappings[] = $row;
      }

      /** @var IndufastProject[] $projects */
      $projects = AppModel::mapObjectIds(IndufastProject::find_all_by(['id' => array_column($projectEventMappings, 'id')]));
      foreach ($projects as $project) {
        $project->load();
      }

      /** @var IndufastCalendarEvent[] $indufastEvents */
      $indufastEvents = AppModel::mapObjectIds(IndufastCalendarEvent::find_all_by(['id' => array_column($projectEventMappings, 'event_id')]));

      foreach ($projectEventMappings as $mapping) {
        $events[$mapping['google_calendar_event_id']]['project'] = $projects[$mapping['id']] ?? null;
        $events[$mapping['google_calendar_event_id']]['indufast_event'] = $indufastEvents[$mapping['event_id']] ?? null;
      }
    }

    static protected function addIndufastEmployees(array &$events): void {
      $attendeesEmail = array_unique(array_map(function ($item) {
        return $item[0]['email'];
      }, array_filter(array_column($events, 'attendees'))));

      $employees = AppModel::mapObjectIds(IndufastEmployee::find_all_by(['email' => $attendeesEmail]), 'email');

      foreach ($events as &$event) {
        foreach ($event['attendees'] as &$attendee) {
          $attendee['employee'] = $employees[$attendee['email']] ?? null;
        }
      }
    }

    /**
     * @return IndufastGoogleDriveFile[]
     */
    public function google_drive_files(): array {
      return IndufastGoogleDriveFile::find_all_by(['calendar_event_id' => $this->id], 'ORDER BY name');
    }

    public function getGoogleFiles(): array {
      if ($this->type !== self::TYPE_WORK) return [];

      $filesByEvent = self::getGoogleFilesBatch([$this]);
      return $filesByEvent[$this->id] ?? [];
    }

    public static function getGoogleFilesBatch(array $indufastEvents): array {
      if (empty($indufastEvents)) return [];

      $filesByEvent = new GoogleCalendarService()->getGoogleEventFilesBatch($indufastEvents);
      return array_map(fn($files) => array_map(fn($file) => (object)[
        'id' => $file['fileId'],
        'name' => $file['title'],
        'url' => $file['fileUrl'],
        'icon_url' => $file['iconLink'],
      ], $files), $filesByEvent);
    }
  }