<?php

  AppModel::loadModelClass('IndufastCalendarEventEmployeeModel');

  class IndufastCalendarEventEmployee extends IndufastCalendarEventEmployeeModel {

    use PropertyCastTrait {
      castProperties as castPropertiesTrait;
    }
    use ModelFillTrait;
    use ValidationTrait;

    protected array $fillable = [
      'calendar_event_id' => 'required|integer|exists:indufast_calendar_event,id',
      'employee_id'       => 'required|integer|exists:indufast_employee,id',
    ];

    const CAST_PROPERTIES = [
      'id'                => 'int',
      'calendar_event_id' => 'int',
      'employee_id'       => 'int',
      'conflict'         =>  'object',
      'from_db'           => 'hidden',
    ];

    public IndufastEmployee $employee;
    public IndufastCalendarEvent $calendar_event;

    public function castProperties(): void {
      $this->employee();
      $this->castPropertiesTrait();
    }

    public function employee(): IndufastEmployee {
      if ($employee = IndufastEmployee::find_by_id($this->employee_id)) {
        return $this->employee = $employee;
      }

      throw new Exception("Employee not found");
    }

    public function event(): IndufastCalendarEvent {
      if ($calendar_event = IndufastCalendarEvent::find_by_id($this->calendar_event_id)) {
        return $this->calendar_event = $calendar_event;
      }

      throw new Exception("Event not found");
    }

    public function conflict(): object|null {
      if (is_string($this->conflict)) {
        $this->conflict = json_decode($this->conflict);
      }
      return $this->conflict;
    }
  }