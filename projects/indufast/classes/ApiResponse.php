<?php

  namespace classes;

  use RestUtils;

  class ApiResponse extends RestUtils {
    use \PropertyCastTrait;

    const HTTP_OK = 200;
    const HTTP_BAD_REQUEST = 400;
    const HTTP_NOT_AUTHORIZED = 401;
    const HTTP_ACCESS_DENIED = 403;
    const HTTP_NOT_FOUND = 404;
    const MESSAGE_OK = "OK";

    /**
     * @param array $data
     */
    public static function sendResponseOK(string $message, $data = []): void {
      self::castAllModels($data);

      RestUtils::sendResponse(self::HTTP_OK, json_encode([
        'message' => $message,
        'data'    => $data,
      ]));
    }

    /**
     * @param array $data
     */
    public static function sendResponseError(string $message, $data = []): void {
      RestUtils::sendResponse(self::HTTP_BAD_REQUEST, json_encode([
        'message' => $message,
        'data'    => $data,
      ]));
    }

    public static function sendResponseNotFound(string $message): void {
      RestUtils::sendResponse(self::HTTP_NOT_FOUND, json_encode([
        'message' => $message,
      ]));
    }

    public static function sendResponseUnauthorized(): void {
      RestUtils::sendResponse(self::HTTP_NOT_AUTHORIZED, json_encode([
        'message' => '401 Unauthorized',
      ]));
    }

    /**
     * @return void
     */
    public static function sendAccessDeniedResponse(): void {
      RestUtils::sendResponse(self::HTTP_ACCESS_DENIED);
    }

  }